package com.anginatech.textrepeater;

import android.database.Cursor;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.List;


public class Sad_Fragment extends Fragment {

    // private DatabaseHelper databaseHelper; // Removed - now using Room database
    private RecyclerView recyclerView;
    private SmsAdapter adapter;
    private List<String> smsList = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
       View myView = inflater.inflate(R.layout.fragment_sad_, container, false);
       if (container!=null){
           container.removeAllViews();
       }


        // databaseHelper = new DatabaseHelper(getContext()); // Removed - now using Room database
        recyclerView = myView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false));
        recyclerView.setHasFixedSize(true);

        loadSms("sadTable");



       return myView;
    }


    private void loadSms(String catagory) {
        Cursor cursor = databaseHelper.getAllDatabyCatagory(catagory);

            if (cursor.moveToFirst()&&cursor!=null) {
                do {
                    String message = cursor.getString(1);
                    smsList.add(message);
                } while (cursor.moveToNext());
            }else {
                Toast.makeText(getContext(), "Something went wrong!Please Check your Internet connection", Toast.LENGTH_SHORT).show();
            }
            cursor.close();




        adapter = new SmsAdapter(getContext(), smsList);
        recyclerView.setAdapter(adapter);
    }





}