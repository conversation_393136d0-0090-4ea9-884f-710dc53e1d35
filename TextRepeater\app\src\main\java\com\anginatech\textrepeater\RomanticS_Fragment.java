package com.anginatech.textrepeater;

import android.database.Cursor;
import android.os.Bundle;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.List;

public class RomanticS_Fragment extends Fragment {


    // private DatabaseHelper databaseHelper; // Removed - now using Room database
    private RecyclerView recyclerView;
    private SmsAdapter adapter;
    private List<String> smsList = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View my_View = inflater.inflate(R.layout.fragment_romantic_s_, container, false);
        if (container!=null) {
            container.removeAllViews();
        }


        // databaseHelper = new DatabaseHelper(getContext()); // Removed - now using Room database
        recyclerView = my_View.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false));
        recyclerView.setHasFixedSize(true);

        loadSms("romanticTable");

        return my_View;
    }


    private void loadSms(String catagory) {
        Cursor cursor = databaseHelper.getAllDatabyCatagory(catagory);
        if (cursor.moveToFirst()&&cursor!=null) {
            do {
                String message = cursor.getString(1);
                smsList.add(message);
            } while (cursor.moveToNext());
        }else {
            Toast.makeText(getContext(), "Something went wrong!Please Check your Internet connection", Toast.LENGTH_SHORT).show();
        }
        cursor.close();

            adapter = new SmsAdapter(getContext(), smsList);
            recyclerView.setAdapter(adapter);


    }



}
