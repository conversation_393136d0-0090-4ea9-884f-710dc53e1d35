package com.anginatech.textrepeater;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.widget.ViewPager2;

import com.anginatech.textrepeater.models.Category;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.List;

public class SMSBottomSheetDialog extends BottomSheetDialogFragment {

    // private DatabaseHelper databaseHelper; // Removed - now using Room database
    // private DynamicDatabaseHelper dynamicDatabaseHelper; // Removed - now using Room database
    private List<String> tabTitles = new ArrayList<>();

    public static SMSBottomSheetDialog newInstance() {
        return new SMSBottomSheetDialog();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.dialog_sms_bottom_sheet, container, false);

        // Initialize both database helpers
        databaseHelper = new DatabaseHelper(requireContext());
        dynamicDatabaseHelper = new DynamicDatabaseHelper(requireContext());

        // Fetch all available data
        List<List<String>> allMessages = getAllMessagesFromDatabases();

        if (allMessages.isEmpty()) {
            Toast.makeText(requireContext(), "No messages available in database", Toast.LENGTH_SHORT).show();
            return view;
        }

        ViewPager2 viewPager = view.findViewById(R.id.viewPagerDialog1);
        SMSPagerAdapter pagerAdapter = new SMSPagerAdapter(this, allMessages);
        viewPager.setAdapter(pagerAdapter);

        TabLayout tabLayout = view.findViewById(R.id.tabLayoutDialog1);

        // Configure TabLayout for better display
        configureTabLayout(tabLayout);

        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            if (position < tabTitles.size()) {
                tab.setText(tabTitles.get(position));
            }
        }).attach();

        // Post-configuration to ensure proper tab display
        configureTabTextDisplay(tabLayout);

        return view;
    }

    private List<List<String>> getAllMessagesFromDatabases() {
        List<List<String>> allMessages = new ArrayList<>();
        tabTitles.clear();

        try {
            // First, try to get dynamic categories
            List<Category> dynamicCategories = dynamicDatabaseHelper.getAllCategories();

            if (!dynamicCategories.isEmpty()) {
                Log.d("SMSBottomSheet", "Loading dynamic categories: " + dynamicCategories.size());

                // Load messages from dynamic database
                for (Category category : dynamicCategories) {
                    List<String> messages = dynamicDatabaseHelper.getMessagesAsStringList(category.getName());
                    if (!messages.isEmpty()) {
                        allMessages.add(messages);
                        // Use compact display name for better tab appearance
                        tabTitles.add(category.getCompactDisplayName());
                        Log.d("SMSBottomSheet", "Added dynamic category: " + category.getDisplayName() + " with " + messages.size() + " messages");
                    }
                }
            }

            // Also load legacy data (if exists) to ensure all data is shown
            List<String> romanticMessages = databaseHelper.getMessages("romanticTable");
            List<String> sadMessages = databaseHelper.getMessages("sadTable");
            List<String> funnyMessages = databaseHelper.getMessages("funnyTable");

            // Add legacy data if it exists and isn't already covered by dynamic categories
            if (!romanticMessages.isEmpty() && !isDuplicateCategory("romantic")) {
                allMessages.add(romanticMessages);
                tabTitles.add("Romantic ❤️");
                Log.d("SMSBottomSheet", "Added legacy romantic messages: " + romanticMessages.size());
            }

            if (!sadMessages.isEmpty() && !isDuplicateCategory("sad")) {
                allMessages.add(sadMessages);
                tabTitles.add("Sad 😢");
                Log.d("SMSBottomSheet", "Added legacy sad messages: " + sadMessages.size());
            }

            if (!funnyMessages.isEmpty() && !isDuplicateCategory("funny")) {
                allMessages.add(funnyMessages);
                tabTitles.add("Funny 😄");
                Log.d("SMSBottomSheet", "Added legacy funny messages: " + funnyMessages.size());
            }

            // If no data found anywhere, show fallback message
            if (allMessages.isEmpty()) {
                Log.w("SMSBottomSheet", "No messages found in any database");
                Toast.makeText(requireContext(), "No messages available. Please check your internet connection or contact support.", Toast.LENGTH_LONG).show();
            }

        } catch (Exception e) {
            Log.e("SMSBottomSheet", "Error loading messages from databases: " + e.getMessage(), e);
            Toast.makeText(requireContext(), "Error loading messages: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }

        Log.d("SMSBottomSheet", "Total categories loaded: " + allMessages.size());
        return allMessages;
    }

    private boolean isDuplicateCategory(String legacyCategory) {
        // Check if we already have a similar category from dynamic database
        for (String title : tabTitles) {
            String lowerTitle = title.toLowerCase();
            if (lowerTitle.contains(legacyCategory.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Configure TabLayout for better display and scrollability
     */
    private void configureTabLayout(TabLayout tabLayout) {
        // Ensure TabLayout is properly configured for scrollable mode
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_START);

        // Additional configuration for better appearance
        tabLayout.setTabRippleColor(null); // Remove ripple for cleaner look
    }

    /**
     * Configure tab text display for better readability
     */
    private void configureTabTextDisplay(TabLayout tabLayout) {
        tabLayout.post(() -> {
            for (int i = 0; i < tabLayout.getTabCount(); i++) {
                TabLayout.Tab tab = tabLayout.getTabAt(i);
                if (tab != null && tab.view != null) {
                    // Allow tabs to expand to show full text
                    tab.view.setMinimumWidth(0);

                    // Find TextView within the tab view and configure it
                    android.widget.TextView textView = findTextViewInTab(tab.view);
                    if (textView != null) {
                        textView.setEllipsize(null);
                        textView.setSingleLine(false);
                        textView.setMaxLines(2);
                        textView.setGravity(android.view.Gravity.CENTER);
                        textView.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 14f);
                        textView.setPadding(8, 4, 8, 4);
                    }
                }
            }
        });
    }

    /**
     * Find TextView in tab view for configuration
     */
    private android.widget.TextView findTextViewInTab(android.view.View tabView) {
        if (tabView instanceof android.widget.TextView) {
            return (android.widget.TextView) tabView;
        }
        if (tabView instanceof android.view.ViewGroup) {
            android.view.ViewGroup group = (android.view.ViewGroup) tabView;
            for (int i = 0; i < group.getChildCount(); i++) {
                android.view.View child = group.getChildAt(i);
                if (child instanceof android.widget.TextView) {
                    return (android.widget.TextView) child;
                } else if (child instanceof android.view.ViewGroup) {
                    android.widget.TextView result = findTextViewInTab(child);
                    if (result != null) {
                        return result;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Refresh the dialog with updated data from databases
     */
    public void refreshData() {
        if (getView() != null) {
            ViewPager2 viewPager = getView().findViewById(R.id.viewPagerDialog1);
            TabLayout tabLayout = getView().findViewById(R.id.tabLayoutDialog1);

            // Reload data
            List<List<String>> allMessages = getAllMessagesFromDatabases();

            if (!allMessages.isEmpty()) {
                // Update adapter
                SMSPagerAdapter pagerAdapter = new SMSPagerAdapter(this, allMessages);
                viewPager.setAdapter(pagerAdapter);

                // Configure TabLayout for better display
                configureTabLayout(tabLayout);

                // Update tabs
                new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
                    if (position < tabTitles.size()) {
                        tab.setText(tabTitles.get(position));
                    }
                }).attach();

                // Post-configuration to ensure proper tab display
                configureTabTextDisplay(tabLayout);

                Toast.makeText(requireContext(), "Data refreshed successfully", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(requireContext(), "No data available to refresh", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * Get total count of all messages across all categories
     */
    public int getTotalMessageCount() {
        int totalCount = 0;
        try {
            // Count from dynamic database
            totalCount += dynamicDatabaseHelper.getTotalMessageCount();

            // Count from legacy database
            List<String> romanticMessages = databaseHelper.getMessages("romanticTable");
            List<String> sadMessages = databaseHelper.getMessages("sadTable");
            List<String> funnyMessages = databaseHelper.getMessages("funnyTable");

            totalCount += romanticMessages.size() + sadMessages.size() + funnyMessages.size();

        } catch (Exception e) {
            Log.e("SMSBottomSheet", "Error counting messages: " + e.getMessage());
        }
        return totalCount;
    }
}