package com.anginatech.textrepeater.fragments;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.anginatech.textrepeater.MyApplication;
import com.anginatech.textrepeater.R;
import com.anginatech.textrepeater.SmsAdapter;
import com.anginatech.textrepeater.database.entities.MessageEntity;
import com.anginatech.textrepeater.database.repository.RoomDataRepository;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Modern Room-based Fragment for displaying category messages
 * Replaces the old SQLite-based fragments (RomanticS_Fragment, Sad_Fragment, Funny_Fragment)
 */
public class RoomCategoryFragment extends Fragment {
    
    private static final String TAG = "RoomCategoryFragment";
    private static final String ARG_CATEGORY_NAME = "category_name";
    
    private String categoryName;
    private RoomDataRepository roomRepository;
    private RecyclerView recyclerView;
    private SmsAdapter adapter;
    private List<String> messagesList = new ArrayList<>();
    private CompositeDisposable disposables = new CompositeDisposable();
    
    /**
     * Create new instance of fragment for specific category
     */
    public static RoomCategoryFragment newInstance(String categoryName) {
        RoomCategoryFragment fragment = new RoomCategoryFragment();
        Bundle args = new Bundle();
        args.putString(ARG_CATEGORY_NAME, categoryName);
        fragment.setArguments(args);
        return fragment;
    }
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            categoryName = getArguments().getString(ARG_CATEGORY_NAME);
        }
        
        // Initialize Room repository
        MyApplication app = (MyApplication) requireActivity().getApplication();
        roomRepository = app.getRoomRepository();
        
        Log.d(TAG, "Fragment created for category: " + categoryName);
    }
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_romantic_s_, container, false);
        
        if (container != null) {
            container.removeAllViews();
        }
        
        // Initialize RecyclerView
        recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false));
        recyclerView.setHasFixedSize(true);
        
        // Initialize adapter
        adapter = new SmsAdapter(requireContext(), messagesList);
        recyclerView.setAdapter(adapter);
        
        // Load messages for this category
        loadMessagesFromRoom();
        
        return view;
    }
    
    /**
     * Load messages from Room database for the specified category
     */
    private void loadMessagesFromRoom() {
        if (categoryName == null || roomRepository == null) {
            Log.w(TAG, "Category name or repository is null");
            showErrorMessage("Category not specified");
            return;
        }
        
        Log.d(TAG, "Loading messages for category: " + categoryName);
        
        // Subscribe to messages for this category
        disposables.add(
            roomRepository.getMessagesByCategory(categoryName)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    this::onMessagesLoaded,
                    this::onError
                )
        );
    }
    
    /**
     * Handle successful message loading
     */
    private void onMessagesLoaded(List<MessageEntity> messageEntities) {
        messagesList.clear();
        
        if (messageEntities.isEmpty()) {
            Log.w(TAG, "No messages found for category: " + categoryName);
            showErrorMessage("No messages available for " + categoryName);
            return;
        }
        
        // Convert MessageEntity to String list for adapter
        for (MessageEntity entity : messageEntities) {
            messagesList.add(entity.getMessage());
        }
        
        // Update adapter
        adapter.notifyDataSetChanged();
        
        Log.d(TAG, "Loaded " + messagesList.size() + " messages for category: " + categoryName);
    }
    
    /**
     * Handle error during message loading
     */
    private void onError(Throwable throwable) {
        Log.e(TAG, "Error loading messages for category " + categoryName + ": " + throwable.getMessage(), throwable);
        showErrorMessage("Failed to load messages. Please check your internet connection and try again.");
    }
    
    /**
     * Show error message to user
     */
    private void showErrorMessage(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * Refresh messages from database
     */
    public void refreshMessages() {
        if (roomRepository != null && categoryName != null) {
            loadMessagesFromRoom();
        }
    }
    
    /**
     * Get the category name for this fragment
     */
    public String getCategoryName() {
        return categoryName;
    }
    
    /**
     * Get the number of messages in this category
     */
    public int getMessageCount() {
        return messagesList.size();
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // Clean up RxJava subscriptions
        disposables.clear();
        Log.d(TAG, "Fragment destroyed for category: " + categoryName);
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        // Final cleanup
        disposables.dispose();
    }
}
