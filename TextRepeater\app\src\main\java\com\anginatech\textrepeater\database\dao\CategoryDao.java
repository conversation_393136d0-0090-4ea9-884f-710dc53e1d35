package com.anginatech.textrepeater.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.room.Transaction;

import com.anginatech.textrepeater.database.entities.CategoryEntity;

import java.util.List;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;

/**
 * Data Access Object for Category operations
 * Provides reactive database operations using RxJava
 */
@Dao
public interface CategoryDao {
    
    /**
     * Get all categories ordered by sort order
     */
    @Query("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC, display_name ASC")
    Flowable<List<CategoryEntity>> getAllCategories();
    
    /**
     * Get all categories (including inactive) for admin purposes
     */
    @Query("SELECT * FROM categories ORDER BY sort_order ASC, display_name ASC")
    Flowable<List<CategoryEntity>> getAllCategoriesIncludingInactive();
    
    /**
     * Get category by name
     */
    @Query("SELECT * FROM categories WHERE name = :name LIMIT 1")
    Single<CategoryEntity> getCategoryByName(String name);
    
    /**
     * Get category by ID
     */
    @Query("SELECT * FROM categories WHERE id = :id LIMIT 1")
    Single<CategoryEntity> getCategoryById(int id);
    
    /**
     * Check if category exists by name
     */
    @Query("SELECT COUNT(*) FROM categories WHERE name = :name")
    Single<Integer> getCategoryCountByName(String name);
    
    /**
     * Get categories count
     */
    @Query("SELECT COUNT(*) FROM categories WHERE is_active = 1")
    Single<Integer> getActiveCategoriesCount();
    
    /**
     * Get total categories count (including inactive)
     */
    @Query("SELECT COUNT(*) FROM categories")
    Single<Integer> getTotalCategoriesCount();
    
    /**
     * Insert single category
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Completable insertCategory(CategoryEntity category);
    
    /**
     * Insert multiple categories
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Completable insertCategories(List<CategoryEntity> categories);
    
    /**
     * Update category
     */
    @Update
    Completable updateCategory(CategoryEntity category);
    
    /**
     * Delete category
     */
    @Delete
    Completable deleteCategory(CategoryEntity category);
    
    /**
     * Delete category by name
     */
    @Query("DELETE FROM categories WHERE name = :name")
    Completable deleteCategoryByName(String name);
    
    /**
     * Delete all categories - COMPLETE DATA REPLACEMENT
     */
    @Query("DELETE FROM categories")
    Completable deleteAllCategories();
    
    /**
     * Deactivate category instead of deleting
     */
    @Query("UPDATE categories SET is_active = 0, updated_at = :timestamp WHERE name = :name")
    Completable deactivateCategory(String name, long timestamp);
    
    /**
     * Activate category
     */
    @Query("UPDATE categories SET is_active = 1, updated_at = :timestamp WHERE name = :name")
    Completable activateCategory(String name, long timestamp);
    
    /**
     * Update category sort order
     */
    @Query("UPDATE categories SET sort_order = :sortOrder, updated_at = :timestamp WHERE name = :name")
    Completable updateCategorySortOrder(String name, int sortOrder, long timestamp);
    
    /**
     * Get categories by sort order range
     */
    @Query("SELECT * FROM categories WHERE is_active = 1 AND sort_order BETWEEN :minOrder AND :maxOrder ORDER BY sort_order ASC")
    Flowable<List<CategoryEntity>> getCategoriesBySortOrderRange(int minOrder, int maxOrder);
    
    /**
     * Search categories by display name
     */
    @Query("SELECT * FROM categories WHERE is_active = 1 AND display_name LIKE '%' || :searchTerm || '%' ORDER BY sort_order ASC")
    Flowable<List<CategoryEntity>> searchCategoriesByDisplayName(String searchTerm);
    
    /**
     * Get recently updated categories
     */
    @Query("SELECT * FROM categories WHERE is_active = 1 AND updated_at > :timestamp ORDER BY updated_at DESC")
    Flowable<List<CategoryEntity>> getRecentlyUpdatedCategories(long timestamp);
    
    /**
     * COMPLETE DATA REPLACEMENT TRANSACTION
     * Ensures atomic operation for fresh data sync
     * Note: Transaction methods must be synchronous, so we handle this in the repository layer
     */
    
    /**
     * Get category statistics
     */
    @Query("SELECT " +
           "COUNT(*) as total_categories, " +
           "COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_categories, " +
           "COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_categories, " +
           "MAX(updated_at) as last_updated " +
           "FROM categories")
    Single<CategoryStats> getCategoryStats();
    
    /**
     * Category statistics data class
     */
    class CategoryStats {
        public int total_categories;
        public int active_categories;
        public int inactive_categories;
        public long last_updated;
        
        @Override
        public String toString() {
            return "CategoryStats{" +
                    "total=" + total_categories +
                    ", active=" + active_categories +
                    ", inactive=" + inactive_categories +
                    ", lastUpdated=" + last_updated +
                    '}';
        }
    }
}
