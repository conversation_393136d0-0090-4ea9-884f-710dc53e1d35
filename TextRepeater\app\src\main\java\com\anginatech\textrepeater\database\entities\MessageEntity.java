package com.anginatech.textrepeater.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

/**
 * Room Entity for Messages
 * Replaces the old SQLite message tables with modern Room database
 */
@Entity(
    tableName = "messages",
    indices = {
        @Index(value = "server_id", unique = true),
        @Index(value = "category_name"),
        @Index(value = "sort_order"),
        @Index(value = {"category_name", "sort_order"})
    },
    foreignKeys = @ForeignKey(
        entity = CategoryEntity.class,
        parentColumns = "name",
        childColumns = "category_name",
        onDelete = ForeignKey.CASCADE
    )
)
public class MessageEntity {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    private int id;
    
    @ColumnInfo(name = "server_id")
    private String serverId;
    
    @NonNull
    @ColumnInfo(name = "message")
    private String message;
    
    @NonNull
    @ColumnInfo(name = "category_name")
    private String categoryName;
    
    @ColumnInfo(name = "category_id")
    private int categoryId;
    
    @ColumnInfo(name = "sort_order")
    private int sortOrder;
    
    @ColumnInfo(name = "is_favorite")
    private boolean isFavorite;
    
    @ColumnInfo(name = "usage_count")
    private int usageCount;
    
    @ColumnInfo(name = "last_used_at")
    private long lastUsedAt;
    
    @ColumnInfo(name = "created_at")
    private long createdAt;
    
    @ColumnInfo(name = "updated_at")
    private long updatedAt;
    
    // Constructors
    public MessageEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.isFavorite = false;
        this.usageCount = 0;
        this.lastUsedAt = 0;
        this.sortOrder = 0;
    }
    
    @Ignore
    public MessageEntity(@NonNull String message, @NonNull String categoryName) {
        this();
        this.message = message;
        this.categoryName = categoryName;
    }

    @Ignore
    public MessageEntity(String serverId, @NonNull String message, @NonNull String categoryName, int categoryId, int sortOrder) {
        this();
        this.serverId = serverId;
        this.message = message;
        this.categoryName = categoryName;
        this.categoryId = categoryId;
        this.sortOrder = sortOrder;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getServerId() {
        return serverId;
    }
    
    public void setServerId(String serverId) {
        this.serverId = serverId;
        this.updatedAt = System.currentTimeMillis();
    }
    
    @NonNull
    public String getMessage() {
        return message;
    }
    
    public void setMessage(@NonNull String message) {
        this.message = message;
        this.updatedAt = System.currentTimeMillis();
    }
    
    @NonNull
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(@NonNull String categoryName) {
        this.categoryName = categoryName;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public int getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public int getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public boolean isFavorite() {
        return isFavorite;
    }
    
    public void setFavorite(boolean favorite) {
        isFavorite = favorite;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public int getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(int usageCount) {
        this.usageCount = usageCount;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public long getLastUsedAt() {
        return lastUsedAt;
    }
    
    public void setLastUsedAt(long lastUsedAt) {
        this.lastUsedAt = lastUsedAt;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    /**
     * Increment usage count and update last used time
     */
    public void incrementUsage() {
        this.usageCount++;
        this.lastUsedAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    /**
     * Check if message is recently used (within last 24 hours)
     */
    public boolean isRecentlyUsed() {
        long oneDayAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000);
        return lastUsedAt > oneDayAgo;
    }
    
    /**
     * Get message preview (first 50 characters)
     */
    public String getMessagePreview() {
        if (message.length() <= 50) {
            return message;
        }
        return message.substring(0, 47) + "...";
    }
    
    @Override
    public String toString() {
        return "MessageEntity{" +
                "id=" + id +
                ", serverId='" + serverId + '\'' +
                ", message='" + getMessagePreview() + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", categoryId=" + categoryId +
                ", sortOrder=" + sortOrder +
                ", isFavorite=" + isFavorite +
                ", usageCount=" + usageCount +
                ", lastUsedAt=" + lastUsedAt +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
