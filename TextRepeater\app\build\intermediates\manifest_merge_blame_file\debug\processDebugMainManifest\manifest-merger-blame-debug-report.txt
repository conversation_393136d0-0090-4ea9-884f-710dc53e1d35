1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anginatech.textrepeater"
4    android:versionCode="3"
5    android:versionName="3.9.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:5-76
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:22-73
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:5-66
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:5-78
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:5-82
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:22-79
17
18    <!-- Notification permission for Android 13+ (API 33+) -->
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:5-77
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:22-74
20
21    <supports-screens
21-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:15:5-18:11
22        android:anyDensity="true"
22-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:16:9-34
23        android:resizeable="true" />
23-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:17:9-34
24
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
28    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
29    <queries>
29-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
30
31        <!-- For browser content -->
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent>
39        <!-- End of browser content -->
40        <!-- For CustomTabsService -->
41        <intent>
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
42            <action android:name="android.support.customtabs.action.CustomTabsService" />
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
43        </intent>
44        <!-- End of CustomTabsService -->
45        <!-- For MRAID capabilities -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
47            <action android:name="android.intent.action.INSERT" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
48
49            <data android:mimeType="vnd.android.cursor.dir/event" />
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
50        </intent>
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
52            <action android:name="android.intent.action.VIEW" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
53
54            <data android:scheme="sms" />
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
57            <action android:name="android.intent.action.DIAL" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
58
59            <data android:path="tel:" />
59-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
60        </intent>
61        <!-- End of MRAID capabilities -->
62    </queries>
63
64    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
65    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
66
67    <permission
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
68        android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
72
73    <application
73-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:20:5-149:19
74        android:name="com.anginatech.textrepeater.MyApplication"
74-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:21:9-38
75        android:allowBackup="true"
75-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:22:9-35
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
77        android:dataExtractionRules="@xml/data_extraction_rules"
77-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:23:9-65
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:fullBackupContent="@xml/backup_rules"
80-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:24:9-54
81        android:icon="@mipmap/ic_launcher"
81-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:25:9-43
82        android:label="@string/app_name"
82-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:26:9-41
83        android:roundIcon="@mipmap/ic_launcher_round"
83-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:27:9-54
84        android:supportsRtl="true"
84-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:28:9-35
85        android:theme="@style/Theme.TextRepeater"
85-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:29:9-50
86        android:usesCleartextTraffic="true" >
86-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:30:9-44
87        <activity
87-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:32:9-37:15
88            android:name="com.anginatech.textrepeater.Decoration_Text_Activity"
88-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:33:13-53
89            android:configChanges="uiMode|screenSize|orientation"
89-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:35:13-66
90            android:exported="false"
90-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:34:13-37
91            android:windowSoftInputMode="adjustPan" />
91-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:36:13-52
92        <activity
92-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:38:9-43:15
93            android:name="com.anginatech.textrepeater.Stylish_Font_Activity"
93-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:39:13-50
94            android:configChanges="uiMode|screenSize|orientation"
94-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:40:13-66
95            android:exported="false"
95-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:41:13-37
96            android:windowSoftInputMode="adjustPan" />
96-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:42:13-52
97        <activity
97-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:44:9-49:15
98            android:name="com.anginatech.textrepeater.Text_to_Imoji_Activity"
98-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:45:13-51
99            android:configChanges="uiMode|screenSize|orientation"
99-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:46:13-66
100            android:exported="false"
100-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:47:13-37
101            android:windowSoftInputMode="adjustPan" />
101-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:48:13-52
102        <activity
102-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:50:9-55:15
103            android:name="com.anginatech.textrepeater.Blank_Text_Activity"
103-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:51:13-48
104            android:configChanges="uiMode|screenSize|orientation"
104-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:52:13-66
105            android:exported="false"
105-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:53:13-37
106            android:windowSoftInputMode="adjustPan" />
106-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:54:13-52
107        <activity
107-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:56:9-61:15
108            android:name="com.anginatech.textrepeater.Emoji_Art"
108-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:57:13-38
109            android:configChanges="uiMode|screenSize|orientation"
109-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:58:13-66
110            android:exported="false"
110-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:59:13-37
111            android:windowSoftInputMode="adjustPan" />
111-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:60:13-52
112        <activity
112-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:62:9-67:15
113            android:name="com.anginatech.textrepeater.Text_Repeat"
113-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:63:13-40
114            android:configChanges="uiMode|screenSize|orientation"
114-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:64:13-66
115            android:exported="false"
115-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:65:13-37
116            android:windowSoftInputMode="adjustPan" />
116-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:66:13-52
117        <activity
117-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:68:9-73:15
118            android:name="com.anginatech.textrepeater.Random_Text_Activity"
118-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:69:13-49
119            android:configChanges="uiMode|screenSize|orientation"
119-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:70:13-66
120            android:exported="false"
120-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:71:13-37
121            android:windowSoftInputMode="adjustPan" />
121-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:72:13-52
122        <activity
122-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:74:9-79:15
123            android:name="com.anginatech.textrepeater.Message_Activity"
123-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:75:13-45
124            android:configChanges="uiMode|screenSize|orientation"
124-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:76:13-66
125            android:exported="false"
125-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:77:13-37
126            android:windowSoftInputMode="adjustPan" />
126-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:78:13-52
127        <activity
127-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:80:9-85:15
128            android:name="com.anginatech.textrepeater.Settings_Activity"
128-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:81:13-46
129            android:configChanges="uiMode|screenSize|orientation"
129-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:82:13-66
130            android:exported="false"
130-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:83:13-37
131            android:windowSoftInputMode="adjustPan" />
131-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:84:13-52
132        <activity
132-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:86:9-91:15
133            android:name="com.anginatech.textrepeater.Navigation_Activity"
133-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:87:13-48
134            android:configChanges="uiMode|screenSize|orientation"
134-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:88:13-66
135            android:exported="false"
135-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:89:13-37
136            android:windowSoftInputMode="adjustPan" />
136-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:90:13-52
137        <activity
137-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:92:9-103:20
138            android:name="com.anginatech.textrepeater.Splash_Screen"
138-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:93:13-42
139            android:configChanges="uiMode|screenSize|orientation"
139-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:94:13-66
140            android:exported="true"
140-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:95:13-36
141            android:windowSoftInputMode="adjustPan" >
141-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:96:13-52
142            <intent-filter>
142-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:98:13-102:29
143                <action android:name="android.intent.action.MAIN" />
143-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:17-69
143-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:25-66
144
145                <category android:name="android.intent.category.LAUNCHER" />
145-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:17-77
145-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:27-74
146            </intent-filter>
147        </activity>
148        <activity
148-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:104:9-109:15
149            android:name="com.anginatech.textrepeater.MainActivity"
149-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:105:13-41
150            android:configChanges="uiMode|screenSize|orientation"
150-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:106:13-66
151            android:exported="true"
151-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:107:13-36
152            android:windowSoftInputMode="adjustPan" />
152-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:108:13-52
153
154        <meta-data
154-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:112:9-114:57
155            android:name="preloaded_fonts"
155-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:113:13-43
156            android:resource="@array/preloaded_fonts" />
156-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:114:13-54
157        <meta-data
157-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:115:9-117:51
158            android:name="com.google.android.gms.ads.APPLICATION_ID"
158-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:116:13-69
159            android:value="@string/ADMOB_APP_ID" />
159-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:117:13-49
160
161        <property
161-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:119:9-123:15
162            android:name="android.adservices.AD_SERVICES_CONFIG"
162-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:120:13-65
163            android:resource="@xml/gma_ad_services_config" />
163-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:121:13-59
164
165        <!-- Firebase Cloud Messaging Service -->
166        <service
166-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:126:9-132:19
167            android:name="com.anginatech.textrepeater.MyFirebaseMessagingService"
167-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:127:13-55
168            android:exported="false" >
168-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:128:13-37
169            <intent-filter>
169-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
170                <action android:name="com.google.firebase.MESSAGING_EVENT" />
170-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
170-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
171            </intent-filter>
172        </service>
173
174        <!-- Firebase Cloud Messaging default notification icon -->
175        <meta-data
175-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:135:9-137:67
176            android:name="com.google.firebase.messaging.default_notification_icon"
176-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:136:13-83
177            android:resource="@drawable/ic_launcher_foreground" />
177-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:137:13-64
178
179        <!-- Firebase Cloud Messaging default notification color -->
180        <meta-data
180-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:140:9-142:46
181            android:name="com.google.firebase.messaging.default_notification_color"
181-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:141:13-84
182            android:resource="@color/love" />
182-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:142:13-43
183
184        <!-- Firebase Cloud Messaging default notification channel -->
185        <meta-data
185-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:145:9-147:59
186            android:name="com.google.firebase.messaging.default_notification_channel_id"
186-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:146:13-89
187            android:value="text_repeater_notifications" />
187-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:147:13-56
188
189        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
190        <activity
190-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
191            android:name="com.google.android.gms.ads.AdActivity"
191-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
192            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
192-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
193            android:exported="false"
193-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
194            android:theme="@android:style/Theme.Translucent" />
194-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
195
196        <provider
196-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
197            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
197-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
198            android:authorities="com.anginatech.textrepeater.mobileadsinitprovider"
198-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
199            android:exported="false"
199-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
200            android:initOrder="100" />
200-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
201
202        <service
202-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
203            android:name="com.google.android.gms.ads.AdService"
203-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
204            android:enabled="true"
204-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
205            android:exported="false" />
205-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
206
207        <activity
207-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
208            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
208-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
209            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
209-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
210            android:exported="false" />
210-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
211        <activity
211-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
212            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
212-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
213            android:excludeFromRecents="true"
213-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
214            android:exported="false"
214-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
215            android:launchMode="singleTask"
215-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
216            android:taskAffinity=""
216-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
217            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
217-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
218
219        <meta-data
219-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
220            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
220-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
221            android:value="true" />
221-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
222        <meta-data
222-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
223            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
223-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
224            android:value="true" />
224-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
225
226        <receiver
226-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
227            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
227-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
228            android:exported="true"
228-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
229            android:permission="com.google.android.c2dm.permission.SEND" >
229-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
230            <intent-filter>
230-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
231                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
231-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
231-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
232            </intent-filter>
233
234            <meta-data
234-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
235                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
235-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
236                android:value="true" />
236-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
237        </receiver>
238        <!--
239             FirebaseMessagingService performs security checks at runtime,
240             but set to not exported to explicitly avoid allowing another app to call it.
241        -->
242        <service
242-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
243            android:name="com.google.firebase.messaging.FirebaseMessagingService"
243-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
244            android:directBootAware="true"
244-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
245            android:exported="false" >
245-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
246            <intent-filter android:priority="-500" >
246-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
247                <action android:name="com.google.firebase.MESSAGING_EVENT" />
247-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
247-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
248            </intent-filter>
249        </service>
250        <service
250-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
251            android:name="com.google.firebase.components.ComponentDiscoveryService"
251-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
252            android:directBootAware="true"
252-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
253            android:exported="false" >
253-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
254            <meta-data
254-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
255                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
255-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
257            <meta-data
257-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
258                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
258-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
260            <meta-data
260-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
261                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
261-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
263            <meta-data
263-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
264                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
264-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
266            <meta-data
266-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
267                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
267-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
268                android:value="com.google.firebase.components.ComponentRegistrar" />
268-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
269            <meta-data
269-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
270                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
270-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
271                android:value="com.google.firebase.components.ComponentRegistrar" />
271-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
272            <meta-data
272-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
273                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
274                android:value="com.google.firebase.components.ComponentRegistrar" />
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
275            <meta-data
275-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
276                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
276-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
277                android:value="com.google.firebase.components.ComponentRegistrar" />
277-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
278        </service>
279
280        <provider
280-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
281            android:name="com.google.firebase.provider.FirebaseInitProvider"
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
282            android:authorities="com.anginatech.textrepeater.firebaseinitprovider"
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
283            android:directBootAware="true"
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
284            android:exported="false"
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
285            android:initOrder="100" />
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
286
287        <receiver
287-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
288            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
288-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
289            android:enabled="true"
289-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
290            android:exported="false" >
290-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
291        </receiver>
292
293        <service
293-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
294            android:name="com.google.android.gms.measurement.AppMeasurementService"
294-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
295            android:enabled="true"
295-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
296            android:exported="false" />
296-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
297        <service
297-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
298            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
298-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
299            android:enabled="true"
299-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
300            android:exported="false"
300-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
301            android:permission="android.permission.BIND_JOB_SERVICE" />
301-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
302
303        <provider
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
304            android:name="androidx.startup.InitializationProvider"
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
305            android:authorities="com.anginatech.textrepeater.androidx-startup"
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
306            android:exported="false" >
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
307            <meta-data
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
308                android:name="androidx.work.WorkManagerInitializer"
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
309                android:value="androidx.startup" />
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
310            <meta-data
310-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
311                android:name="androidx.emoji2.text.EmojiCompatInitializer"
311-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
312                android:value="androidx.startup" />
312-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
313            <meta-data
313-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
314                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
314-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
315                android:value="androidx.startup" />
315-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
316            <meta-data
316-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
317                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
317-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
318                android:value="androidx.startup" />
318-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
319        </provider>
320
321        <service
321-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
322            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
323            android:directBootAware="false"
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
324            android:enabled="@bool/enable_system_alarm_service_default"
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
325            android:exported="false" />
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
326        <service
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
327            android:name="androidx.work.impl.background.systemjob.SystemJobService"
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
329            android:enabled="@bool/enable_system_job_service_default"
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
330            android:exported="true"
330-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
331            android:permission="android.permission.BIND_JOB_SERVICE" />
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
332        <service
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
333            android:name="androidx.work.impl.foreground.SystemForegroundService"
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
335            android:enabled="@bool/enable_system_foreground_service_default"
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
336            android:exported="false" />
336-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
337
338        <receiver
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
339            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
340            android:directBootAware="false"
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
341            android:enabled="true"
341-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
342            android:exported="false" />
342-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
343        <receiver
343-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
344            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
344-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
346            android:enabled="false"
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
347            android:exported="false" >
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
348            <intent-filter>
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
349                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
350                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
350-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
350-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
351            </intent-filter>
352        </receiver>
353        <receiver
353-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
354            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
355            android:directBootAware="false"
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
356            android:enabled="false"
356-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
357            android:exported="false" >
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
358            <intent-filter>
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
359                <action android:name="android.intent.action.BATTERY_OKAY" />
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
360                <action android:name="android.intent.action.BATTERY_LOW" />
360-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
360-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
361            </intent-filter>
362        </receiver>
363        <receiver
363-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
364            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
364-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
365            android:directBootAware="false"
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
366            android:enabled="false"
366-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
367            android:exported="false" >
367-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
368            <intent-filter>
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
369                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
370                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
370-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
370-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
371            </intent-filter>
372        </receiver>
373        <receiver
373-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
374            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
374-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
375            android:directBootAware="false"
375-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
376            android:enabled="false"
376-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
377            android:exported="false" >
377-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
378            <intent-filter>
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
379                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
379-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
379-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
380            </intent-filter>
381        </receiver>
382        <receiver
382-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
383            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
383-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
384            android:directBootAware="false"
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
385            android:enabled="false"
385-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
386            android:exported="false" >
386-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
387            <intent-filter>
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
388                <action android:name="android.intent.action.BOOT_COMPLETED" />
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
389                <action android:name="android.intent.action.TIME_SET" />
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
390                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
391            </intent-filter>
392        </receiver>
393        <receiver
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
394            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
395            android:directBootAware="false"
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
396            android:enabled="@bool/enable_system_alarm_service_default"
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
397            android:exported="false" >
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
398            <intent-filter>
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
399                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
400            </intent-filter>
401        </receiver>
402        <receiver
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
403            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
404            android:directBootAware="false"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
405            android:enabled="true"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
406            android:exported="true"
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
407            android:permission="android.permission.DUMP" >
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
408            <intent-filter>
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
409                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
410            </intent-filter>
411        </receiver>
412
413        <activity
413-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
414            android:name="com.google.android.gms.common.api.GoogleApiActivity"
414-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
415            android:exported="false"
415-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
416            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
416-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
417
418        <uses-library
418-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
419            android:name="android.ext.adservices"
419-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
420            android:required="false" />
420-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
421
422        <service
422-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
423            android:name="androidx.room.MultiInstanceInvalidationService"
423-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
424            android:directBootAware="true"
424-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
425            android:exported="false" />
425-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
426
427        <meta-data
427-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
428            android:name="com.google.android.gms.version"
428-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
429            android:value="@integer/google_play_services_version" />
429-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
430
431        <service
431-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
432            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
432-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
433            android:exported="false" >
433-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
434            <meta-data
434-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
435                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
435-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
436                android:value="cct" />
436-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
437        </service>
438        <service
438-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
439            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
439-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
440            android:exported="false"
440-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
441            android:permission="android.permission.BIND_JOB_SERVICE" >
441-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
442        </service>
443
444        <receiver
444-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
445            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
445-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
446            android:exported="false" />
446-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
447        <receiver
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
448            android:name="androidx.profileinstaller.ProfileInstallReceiver"
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
449            android:directBootAware="false"
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
450            android:enabled="true"
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
451            android:exported="true"
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
452            android:permission="android.permission.DUMP" >
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
453            <intent-filter>
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
454                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
455            </intent-filter>
456            <intent-filter>
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
457                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
458            </intent-filter>
459            <intent-filter>
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
460                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
461            </intent-filter>
462            <intent-filter>
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
463                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
464            </intent-filter>
465        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
466        <activity
466-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
467            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
467-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
468            android:exported="false"
468-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
469            android:stateNotNeeded="true"
469-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
470            android:theme="@style/Theme.PlayCore.Transparent" />
470-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
471    </application>
472
473</manifest>
