package com.anginatech.textrepeater.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.room.Transaction;

import com.anginatech.textrepeater.database.entities.MessageEntity;

import java.util.List;

import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;

/**
 * Data Access Object for Message operations
 * Provides reactive database operations using RxJava
 */
@Dao
public interface MessageDao {
    
    /**
     * Get all messages for a specific category
     */
    @Query("SELECT * FROM messages WHERE category_name = :categoryName ORDER BY sort_order ASC, id ASC")
    Flowable<List<MessageEntity>> getMessagesByCategory(String categoryName);
    
    /**
     * Get all messages
     */
    @Query("SELECT * FROM messages ORDER BY category_name ASC, sort_order ASC, id ASC")
    Flowable<List<MessageEntity>> getAllMessages();
    
    /**
     * Get message by server ID
     */
    @Query("SELECT * FROM messages WHERE server_id = :serverId LIMIT 1")
    Single<MessageEntity> getMessageByServerId(String serverId);
    
    /**
     * Get message by ID
     */
    @Query("SELECT * FROM messages WHERE id = :id LIMIT 1")
    Single<MessageEntity> getMessageById(int id);
    
    /**
     * Get messages count for category
     */
    @Query("SELECT COUNT(*) FROM messages WHERE category_name = :categoryName")
    Single<Integer> getMessageCountByCategory(String categoryName);
    
    /**
     * Get total messages count
     */
    @Query("SELECT COUNT(*) FROM messages")
    Single<Integer> getTotalMessagesCount();
    
    /**
     * Get favorite messages
     */
    @Query("SELECT * FROM messages WHERE is_favorite = 1 ORDER BY last_used_at DESC, usage_count DESC")
    Flowable<List<MessageEntity>> getFavoriteMessages();
    
    /**
     * Get recently used messages
     */
    @Query("SELECT * FROM messages WHERE last_used_at > 0 ORDER BY last_used_at DESC LIMIT :limit")
    Flowable<List<MessageEntity>> getRecentlyUsedMessages(int limit);
    
    /**
     * Get most used messages
     */
    @Query("SELECT * FROM messages WHERE usage_count > 0 ORDER BY usage_count DESC, last_used_at DESC LIMIT :limit")
    Flowable<List<MessageEntity>> getMostUsedMessages(int limit);
    
    /**
     * Search messages by content
     */
    @Query("SELECT * FROM messages WHERE message LIKE '%' || :searchTerm || '%' ORDER BY category_name ASC, sort_order ASC")
    Flowable<List<MessageEntity>> searchMessages(String searchTerm);
    
    /**
     * Search messages in specific category
     */
    @Query("SELECT * FROM messages WHERE category_name = :categoryName AND message LIKE '%' || :searchTerm || '%' ORDER BY sort_order ASC")
    Flowable<List<MessageEntity>> searchMessagesInCategory(String categoryName, String searchTerm);
    
    /**
     * Insert single message
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Completable insertMessage(MessageEntity message);
    
    /**
     * Insert multiple messages
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Completable insertMessages(List<MessageEntity> messages);
    
    /**
     * Update message
     */
    @Update
    Completable updateMessage(MessageEntity message);
    
    /**
     * Delete message
     */
    @Delete
    Completable deleteMessage(MessageEntity message);
    
    /**
     * Delete message by server ID
     */
    @Query("DELETE FROM messages WHERE server_id = :serverId")
    Completable deleteMessageByServerId(String serverId);
    
    /**
     * Delete all messages for a category
     */
    @Query("DELETE FROM messages WHERE category_name = :categoryName")
    Completable deleteMessagesByCategory(String categoryName);
    
    /**
     * Delete all messages - COMPLETE DATA REPLACEMENT
     */
    @Query("DELETE FROM messages")
    Completable deleteAllMessages();
    
    /**
     * Update message usage
     */
    @Query("UPDATE messages SET usage_count = usage_count + 1, last_used_at = :timestamp, updated_at = :timestamp WHERE id = :messageId")
    Completable incrementMessageUsage(int messageId, long timestamp);
    
    /**
     * Toggle favorite status
     */
    @Query("UPDATE messages SET is_favorite = NOT is_favorite, updated_at = :timestamp WHERE id = :messageId")
    Completable toggleFavorite(int messageId, long timestamp);
    
    /**
     * Set favorite status
     */
    @Query("UPDATE messages SET is_favorite = :isFavorite, updated_at = :timestamp WHERE id = :messageId")
    Completable setFavorite(int messageId, boolean isFavorite, long timestamp);
    
    /**
     * Update message sort order
     */
    @Query("UPDATE messages SET sort_order = :sortOrder, updated_at = :timestamp WHERE id = :messageId")
    Completable updateMessageSortOrder(int messageId, int sortOrder, long timestamp);
    
    /**
     * Get messages by sort order range in category
     */
    @Query("SELECT * FROM messages WHERE category_name = :categoryName AND sort_order BETWEEN :minOrder AND :maxOrder ORDER BY sort_order ASC")
    Flowable<List<MessageEntity>> getMessagesBySortOrderRange(String categoryName, int minOrder, int maxOrder);
    
    /**
     * COMPLETE DATA REPLACEMENT TRANSACTION
     * Ensures atomic operation for fresh data sync
     * Note: Transaction methods must be synchronous, so we handle this in the repository layer
     */
    
    /**
     * Get message statistics
     */
    @Query("SELECT " +
           "COUNT(*) as total_messages, " +
           "COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_messages, " +
           "COUNT(CASE WHEN usage_count > 0 THEN 1 END) as used_messages, " +
           "AVG(usage_count) as avg_usage_count, " +
           "MAX(updated_at) as last_updated " +
           "FROM messages")
    Single<MessageStats> getMessageStats();
    
    /**
     * Get category-wise message statistics
     */
    @Query("SELECT " +
           "category_name, " +
           "COUNT(*) as message_count, " +
           "COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count, " +
           "AVG(usage_count) as avg_usage, " +
           "MAX(updated_at) as last_updated " +
           "FROM messages " +
           "GROUP BY category_name " +
           "ORDER BY category_name")
    Flowable<List<CategoryMessageStats>> getCategoryMessageStats();
    
    /**
     * Message statistics data class
     */
    class MessageStats {
        public int total_messages;
        public int favorite_messages;
        public int used_messages;
        public double avg_usage_count;
        public long last_updated;
        
        @Override
        public String toString() {
            return "MessageStats{" +
                    "total=" + total_messages +
                    ", favorites=" + favorite_messages +
                    ", used=" + used_messages +
                    ", avgUsage=" + avg_usage_count +
                    ", lastUpdated=" + last_updated +
                    '}';
        }
    }
    
    /**
     * Category-wise message statistics data class
     */
    class CategoryMessageStats {
        public String category_name;
        public int message_count;
        public int favorite_count;
        public double avg_usage;
        public long last_updated;
        
        @Override
        public String toString() {
            return "CategoryMessageStats{" +
                    "category='" + category_name + '\'' +
                    ", count=" + message_count +
                    ", favorites=" + favorite_count +
                    ", avgUsage=" + avg_usage +
                    ", lastUpdated=" + last_updated +
                    '}';
        }
    }
}
