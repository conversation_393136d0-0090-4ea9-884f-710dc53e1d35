package com.anginatech.textrepeater.adapters;

import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.anginatech.textrepeater.database.entities.CategoryEntity;
import com.anginatech.textrepeater.fragments.RoomCategoryFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * Modern ViewPager adapter that uses Room database
 * Replaces the old SQLite-based ViewPager adapters
 */
public class RoomViewPagerAdapter extends FragmentStateAdapter {
    
    private static final String TAG = "RoomViewPagerAdapter";
    
    private List<CategoryEntity> categories = new ArrayList<>();
    private List<RoomCategoryFragment> fragments = new ArrayList<>();
    
    public RoomViewPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
        Log.d(TAG, "RoomViewPagerAdapter created");
    }
    
    @NonNull
    @Override
    public Fragment createFragment(int position) {
        if (position < 0 || position >= categories.size()) {
            Log.w(TAG, "Invalid position: " + position + ", categories size: " + categories.size());
            // Return a default fragment or handle error
            return RoomCategoryFragment.newInstance("default");
        }
        
        CategoryEntity category = categories.get(position);
        RoomCategoryFragment fragment = RoomCategoryFragment.newInstance(category.getName());
        
        // Keep track of fragments for potential updates
        if (position < fragments.size()) {
            fragments.set(position, fragment);
        } else {
            fragments.add(fragment);
        }
        
        Log.d(TAG, "Created fragment for category: " + category.getName() + " at position: " + position);
        return fragment;
    }
    
    @Override
    public int getItemCount() {
        return categories.size();
    }
    
    /**
     * Update categories and refresh adapter
     */
    public void updateCategories(List<CategoryEntity> newCategories) {
        if (newCategories == null) {
            Log.w(TAG, "Attempted to update with null categories");
            return;
        }
        
        Log.d(TAG, "Updating categories: " + newCategories.size() + " categories");
        
        this.categories.clear();
        this.categories.addAll(newCategories);
        
        // Clear fragments list to force recreation
        fragments.clear();
        
        // Notify adapter of data change
        notifyDataSetChanged();
        
        Log.d(TAG, "Categories updated successfully");
    }
    
    /**
     * Get category at specific position
     */
    public CategoryEntity getCategoryAt(int position) {
        if (position >= 0 && position < categories.size()) {
            return categories.get(position);
        }
        return null;
    }
    
    /**
     * Get fragment at specific position
     */
    public RoomCategoryFragment getFragmentAt(int position) {
        if (position >= 0 && position < fragments.size()) {
            return fragments.get(position);
        }
        return null;
    }
    
    /**
     * Get all categories
     */
    public List<CategoryEntity> getCategories() {
        return new ArrayList<>(categories);
    }
    
    /**
     * Find position of category by name
     */
    public int findCategoryPosition(String categoryName) {
        if (categoryName == null) {
            return -1;
        }
        
        for (int i = 0; i < categories.size(); i++) {
            if (categoryName.equals(categories.get(i).getName())) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * Check if adapter has categories
     */
    public boolean hasCategories() {
        return !categories.isEmpty();
    }
    
    /**
     * Get total message count across all categories
     */
    public int getTotalMessageCount() {
        int totalCount = 0;
        for (RoomCategoryFragment fragment : fragments) {
            if (fragment != null) {
                totalCount += fragment.getMessageCount();
            }
        }
        return totalCount;
    }
    
    /**
     * Refresh all fragments
     */
    public void refreshAllFragments() {
        Log.d(TAG, "Refreshing all fragments");
        for (RoomCategoryFragment fragment : fragments) {
            if (fragment != null) {
                fragment.refreshMessages();
            }
        }
    }
    
    /**
     * Refresh specific fragment by position
     */
    public void refreshFragment(int position) {
        RoomCategoryFragment fragment = getFragmentAt(position);
        if (fragment != null) {
            fragment.refreshMessages();
            Log.d(TAG, "Refreshed fragment at position: " + position);
        }
    }
    
    /**
     * Refresh specific fragment by category name
     */
    public void refreshFragment(String categoryName) {
        int position = findCategoryPosition(categoryName);
        if (position >= 0) {
            refreshFragment(position);
        }
    }
    
    /**
     * Get adapter statistics for debugging
     */
    public String getAdapterStats() {
        return "RoomViewPagerAdapter{" +
                "categories=" + categories.size() +
                ", fragments=" + fragments.size() +
                ", totalMessages=" + getTotalMessageCount() +
                '}';
    }
    
    @Override
    public long getItemId(int position) {
        // Use category name hash as stable ID
        if (position >= 0 && position < categories.size()) {
            return categories.get(position).getName().hashCode();
        }
        return super.getItemId(position);
    }
    
    @Override
    public boolean containsItem(long itemId) {
        // Check if item with this ID exists
        for (CategoryEntity category : categories) {
            if (category.getName().hashCode() == itemId) {
                return true;
            }
        }
        return false;
    }
}
